const { sql, query, queryWithParams } = require('../service/index.js')
const { isNumber } = require('../utils/index.js')
const { buildUUID, buildShortUUID } = require('../utils/uuid.js')

class PumpHouseController {
  async create(ctx, next) {
    try {
      const bodyData = ctx.request.body
      bodyData.PumpRoomNumber = buildUUID()
      bodyData.PumpRoomID = buildShortUUID('PumpRoomID')
      bodyData.SYSTEMID = buildShortUUID('SYSTEMID')
      bodyData.CurrentNode = 1

      const keys = Object.keys(bodyData).join(',')
      const values = Object.values(bodyData)
        .map((item) => {
          if (item === null) return 'NULL'
          if (isNumber(item)) return item
          return `'${item}'`
        })
        .join(',')
      const sentence = `INSERT INTO dbo.[SecondaryWaterProgressNew] (${keys}) VALUES (${values});`
      await query(sentence)
      const { recordsets } = await queryWithParams(`SELECT * FROM dbo.[SecondaryWaterProgressNew] WHERE PumpRoomNumber = @PumpRoomNumber`, {
        PumpRoomNumber: { type: sql.VarChar, value: bodyData.PumpRoomNumber }
      })
      ctx.body = { code: 200, msg: '创建成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 400, msg: '创建失败', data: error.message }
    }
  }

  async update(ctx, next) {
    try {
      const bodyData = ctx.request.body
      const PumpRoomNumber = bodyData.PumpRoomNumber
      delete bodyData.PumpRoomNumber
      delete bodyData.Id
      delete bodyData.CurrentNode
      delete bodyData.UpdateTime
      bodyData.UpdatePerson = ctx.user.Name
      const data = Object.entries(bodyData)
      const sentence = `UPDATE SecondaryWaterProgressNew SET ${data.map((item) => `${item[0]} = ${item[1] === null || isNumber(item[1]) ? item[1] : `'${item[1]}'`}`).join(', ')} WHERE PumpRoomNumber = '${PumpRoomNumber}';`
      await query(sentence)
      ctx.body = { code: 200, message: '巡检成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '修改失败', data: error.message }
    }
  }

  async gain(ctx, next) {
    try {
      const { PumpRoomNumber } = ctx.params
      const sentence = `SELECT * FROM SecondaryWaterProgressNew WHERE PumpRoomNumber = @PumpRoomNumber`
      const { recordsets } = await queryWithParams(sentence, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })

      // 查询泵房节点
      const { recordsets: nodeRecordsets } = await queryWithParams(`SELECT * FROM SecondaryWaterProgressNode WHERE PumpRoomNumber = @PumpRoomNumber`, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      ctx.body = { code: 200, message: '查询成功', data: recordsets[0][0] }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  async list(ctx, next) {
    try {
      const { startTime, endTime, all, page, pageSize, pumpHouseName } = ctx.query

      // 分页参数处理
      const pageNum = page ? (Number(page) > 0 ? Number(page) : 1) : null
      const sizeNum = pageSize ? (Number(pageSize) > 0 ? Number(pageSize) : 10) : null

      // 构建基础字段
      const fields = all
        ? '*'
        : `PumpRoomNumber, BelongingArea, PumpHouseName, RemouldState,
          Gridding, BelongingStreet, PumpRoomControlledState, AccuratePosition,
          OperationManagementState, ProgressStatus, X, Y,
          ZoneCode, CurrentNode, UpdatePerson, UpdateTime, Batch`

      // 构建WHERE条件
      let whereClause = ''
      const params = {}
      const conditions = []

      if (startTime && endTime) {
        conditions.push('UpdateTime >= @startTime AND UpdateTime <= @endTime')
        params.startTime = { type: sql.DateTime, value: startTime }
        params.endTime = { type: sql.DateTime, value: endTime }
      }

      if (pumpHouseName) {
        conditions.push('PumpHouseName LIKE @pumpHouseName')
        params.pumpHouseName = { type: sql.VarChar, value: `%${pumpHouseName}%` }
      }

      if (conditions.length > 0) {
        whereClause = `WHERE ${conditions.join(' AND ')}`
      }

      // 如果有分页参数，执行分页查询
      if (pageNum && sizeNum) {
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM SecondaryWaterProgressNew ${whereClause}`
        const { recordsets: countSets } = await queryWithParams(countSql, params)
        const total = countSets[0][0].total

        // 查询分页数据
        const offset = (pageNum - 1) * sizeNum
        const dataSql = `SELECT ${fields} FROM SecondaryWaterProgressNew ${whereClause} ORDER BY UpdateTime DESC OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`

        params.offset = { type: sql.Int, value: offset }
        params.sizeNum = { type: sql.Int, value: sizeNum }

        const { recordsets } = await queryWithParams(dataSql, params)

        // 计算分页信息
        const totalPages = Math.ceil(total / sizeNum)

        ctx.body = {
          code: 200,
          message: '查询成功',
          data: recordsets[0],
          pagination: {
            page: pageNum,
            pageSize: sizeNum,
            total,
            totalPages,
            hasNext: pageNum < totalPages,
            hasPrev: pageNum > 1
          }
        }
      } else {
        // 不分页，返回所有数据
        const dataSql = `SELECT ${fields} FROM SecondaryWaterProgressNew ${whereClause} ORDER BY UpdateTime DESC`
        const { recordsets } = await queryWithParams(dataSql, params)
        ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
      }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  async wxlist(ctx, next) {
    try {
      const { all, page, pageSize, pumpHouseName, scope, LowZoneTheoreticalEndPressure, MidZoneTheoreticalEndPressure, HighZoneTheoreticalEndPressure } = ctx.query

      // 分页参数处理
      const pageNum = page ? (Number(page) > 0 ? Number(page) : 1) : null
      const sizeNum = pageSize ? (Number(pageSize) > 0 ? Number(pageSize) : 10) : null

      // 构建基础字段
      const fields = all
        ? '*'
        : `PumpRoomNumber, BelongingArea, PumpHouseName, RemouldState,
          Gridding, BelongingStreet, PumpRoomControlledState, AccuratePosition,
          OperationManagementState, ProgressStatus, X, Y,
          ZoneCode, CurrentNode, UpdatePerson, UpdateTime, Batch`

      // 构建WHERE条件
      let whereClause = ''
      const params = {}
      const conditions = []

      if (pumpHouseName) {
        conditions.push('PumpHouseName LIKE @pumpHouseName')
        params.pumpHouseName = { type: sql.VarChar, value: `%${pumpHouseName}%` }
      }

      // 数据划分筛选
      if (scope) {
        const [key, value] = scope.split(':')
        if (key === 'subject') {
          conditions.push('Gridding LIKE @Gridding')
          params.Gridding = { type: sql.VarChar, value: `%${value}%` }
        } else {
          conditions.push(`${key} = @${key}`)
          params[key] = { type: sql.VarChar, value: value }
        }
      }

      // 处理压力条件（使用OR逻辑）
      const pressureConditions = []
      if (LowZoneTheoreticalEndPressure) {
        pressureConditions.push('LowZoneTheoreticalEndPressure > @LowZoneTheoreticalEndPressure')
        params.LowZoneTheoreticalEndPressure = { type: sql.VarChar, value: LowZoneTheoreticalEndPressure }
      }
      if (MidZoneTheoreticalEndPressure) {
        pressureConditions.push('MidZoneTheoreticalEndPressure > @MidZoneTheoreticalEndPressure')
        params.MidZoneTheoreticalEndPressure = { type: sql.VarChar, value: MidZoneTheoreticalEndPressure }
      }
      if (HighZoneTheoreticalEndPressure) {
        pressureConditions.push('HighZoneTheoreticalEndPressure > @HighZoneTheoreticalEndPressure')
        params.HighZoneTheoreticalEndPressure = { type: sql.VarChar, value: HighZoneTheoreticalEndPressure }
      }

      // 如果有压力条件，将它们用OR连接后作为一个整体条件添加
      if (pressureConditions.length > 0) {
        conditions.push(`(${pressureConditions.join(' OR ')})`)
      }

      if (conditions.length > 0) {
        whereClause = `WHERE ${conditions.join(' AND ')}`
      }

      // 如果有分页参数，执行分页查询
      if (pageNum && sizeNum) {
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM SecondaryWaterProgressNew ${whereClause}`
        const { recordsets: countSets } = await queryWithParams(countSql, params)
        const total = countSets[0][0].total

        // 查询分页数据
        const offset = (pageNum - 1) * sizeNum
        const dataSql = `SELECT ${fields} FROM SecondaryWaterProgressNew ${whereClause} ORDER BY UpdateTime DESC OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`

        params.offset = { type: sql.Int, value: offset }
        params.sizeNum = { type: sql.Int, value: sizeNum }

        const { recordsets } = await queryWithParams(dataSql, params)

        // 计算分页信息
        const totalPages = Math.ceil(total / sizeNum)

        ctx.body = {
          code: 200,
          message: '查询成功',
          data: recordsets[0],
          pagination: {
            page: pageNum,
            pageSize: sizeNum,
            total,
            totalPages,
            hasNext: pageNum < totalPages,
            hasPrev: pageNum > 1
          }
        }
      } else {
        // 不分页，返回所有数据
        const dataSql = `SELECT ${fields} FROM SecondaryWaterProgressNew ${whereClause} ORDER BY UpdateTime DESC`
        const { recordsets } = await queryWithParams(dataSql, params)
        ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
      }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  async seek(ctx, next) {
    try {
      const { pumpHouseName } = ctx.query
      const sentence = `SELECT * FROM SecondaryWaterProgressNew WHERE PumpHouseName LIKE @pumpHouseName`
      const { recordsets } = await queryWithParams(sentence, {
        pumpHouseName: { type: sql.VarChar, value: `%${pumpHouseName}%` }
      })
      ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  async seekScope(ctx, next) {
    try {
      const { ZoneCode } = ctx.query
      const sentence = `SELECT * FROM SecondaryWaterProgressNew WHERE ZoneCode = @ZoneCode`
      const { recordsets } = await queryWithParams(sentence, {
        ZoneCode: { type: sql.VarChar, value: ZoneCode }
      })
      ctx.body = { code: 200, message: '查询成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }
}

class PumpHouseNodeController {
  async create(ctx) {
    try {
      const { PumpRoomNumber, Node, CompletionTime } = ctx.request.body
      if (!PumpRoomNumber || !Node) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await queryWithParams(`SELECT * FROM SecondaryWaterProgressNode WHERE Node = @Node AND PumpRoomNumber = @PumpRoomNumber`, {
        Node: { type: sql.Int, value: Node },
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      if (recordsets[0].length) return (ctx.body = { code: 200, msg: '节点已存在' })
      const sentence = `INSERT INTO dbo.[SecondaryWaterProgressNode] (PumpRoomNumber,IsEnd,Node, CompletionTime, Remark, UpdatePerson)
      VALUES (@PumpRoomNumber,0,@Node,@CompletionTime,NULL,@UpdatePerson)`
      await queryWithParams(sentence, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
        Node: { type: sql.Int, value: Node },
        CompletionTime: { type: sql.DateTime, value: CompletionTime },
        UpdatePerson: { type: sql.VarChar, value: ctx.user.Name }
      })

      const { recordsets: res } = await queryWithParams(`SELECT MAX(Node) AS newestNode FROM SecondaryWaterProgressNode WHERE PumpRoomNumber = @PumpRoomNumber`, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      if (res[0][0].newestNode) {
        await queryWithParams(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = @CurrentNode WHERE PumpRoomNumber = @PumpRoomNumber`, {
          CurrentNode: { type: sql.Int, value: res[0][0].newestNode },
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
        })
      } else {
        await queryWithParams(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = @CurrentNode WHERE PumpRoomNumber = @PumpRoomNumber`, {
          CurrentNode: { type: sql.Int, value: 1 },
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
        })
      }
      ctx.body = { code: 200, message: '添加成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '添加失败', data: error.message }
    }
  }
  async update(ctx) {
    try {
      const { PumpRoomNumber, IsEnd, Node, CompletionTime, Remark } = ctx.request.body
      if (!PumpRoomNumber || !Node || !CompletionTime) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await queryWithParams(`SELECT * FROM SecondaryWaterProgressNode WHERE Node = @Node AND PumpRoomNumber = @PumpRoomNumber`, {
        Node: { type: sql.Int, value: Node },
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      if (recordsets[0].length) {
        const sentence = `UPDATE dbo.[SecondaryWaterProgressNode] SET IsEnd = @IsEnd, CompletionTime = @CompletionTime, Remark = @Remark, UpdatePerson = @UpdatePerson WHERE Node = @Node AND PumpRoomNumber = @PumpRoomNumber`
        await queryWithParams(sentence, {
          IsEnd: { type: sql.Bit, value: IsEnd },
          CompletionTime: { type: sql.DateTime, value: CompletionTime },
          Remark: { type: sql.VarChar, value: Remark },
          UpdatePerson: { type: sql.VarChar, value: ctx.user.Name },
          Node: { type: sql.Int, value: Node },
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
        })
      } else {
        const sentence = `INSERT INTO dbo.[SecondaryWaterProgressNode] (PumpRoomNumber,IsEnd,Node, CompletionTime, Remark, UpdatePerson)
        VALUES (@PumpRoomNumber,@IsEnd,@Node,@CompletionTime,@Remark,@UpdatePerson)`
        await queryWithParams(sentence, {
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
          IsEnd: { type: sql.Bit, value: IsEnd },
          Node: { type: sql.Int, value: Node },
          CompletionTime: { type: sql.DateTime, value: CompletionTime },
          Remark: { type: sql.VarChar, value: Remark },
          UpdatePerson: { type: sql.VarChar, value: ctx.user.Name }
        })
      }

      const { recordsets: res } = await queryWithParams(`SELECT MAX(Node) AS newestNode FROM SecondaryWaterProgressNode WHERE PumpRoomNumber = @PumpRoomNumber AND IsEnd = 1`, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      if (res[0][0].newestNode) {
        await queryWithParams(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = @CurrentNode, UpdatePerson = @UpdatePerson WHERE PumpRoomNumber = @PumpRoomNumber`, {
          CurrentNode: { type: sql.Int, value: res[0][0].newestNode },
          UpdatePerson: { type: sql.VarChar, value: ctx.user.Name },
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
        })
      } else {
        await queryWithParams(`UPDATE dbo.[SecondaryWaterProgressNew] SET CurrentNode = @CurrentNode WHERE PumpRoomNumber = @PumpRoomNumber`, {
          CurrentNode: { type: sql.Int, value: 1 },
          PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
        })
      }

      ctx.body = { code: 200, message: '更新成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '更新失败', data: error.message }
    }
  }
  async gain(ctx) {
    try {
      const { PumpRoomNumber } = ctx.params
      const { Node } = ctx.query
      const sentence = `SELECT
        n.*,
        (SELECT * FROM SecondaryWaterProgressNodeFiles AS f WHERE f.PumpRoomNumber = n.PumpRoomNumber AND f.Node = n.Node FOR JSON PATH) AS Files
        FROM SecondaryWaterProgressNode AS n WHERE n.PumpRoomNumber = @PumpRoomNumber AND n.Node = @Node`
      const { recordsets } = await queryWithParams(sentence, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
        Node: { type: sql.Int, value: Node }
      })
      ctx.body = { code: 200, message: '获取成功', data: recordsets[0][0] ?? null }
    } catch (error) {
      ctx.body = { code: 400, message: '获取失败', data: error.message }
    }
  }
  async list(ctx) {
    try {
      const { PumpRoomNumber } = ctx.params
      const sentence = `SELECT
        n.*,
        (SELECT * FROM SecondaryWaterProgressNodeFiles AS f WHERE f.PumpRoomNumber = n.PumpRoomNumber AND f.Node = n.Node FOR JSON PATH) AS Files
        FROM SecondaryWaterProgressNode AS n WHERE n.PumpRoomNumber = @PumpRoomNumber ORDER BY Node ASC`
      const { recordsets } = await queryWithParams(sentence, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber }
      })
      ctx.body = { code: 200, message: '获取成功', data: recordsets[0] }
    } catch (error) {
      ctx.body = { code: 400, message: '获取失败', data: error.message }
    }
  }
}

// 节点文件管理
class PumpHouseNodeFileController {
  async create(ctx, next) {
    try {
      const { PumpRoomNumber, Node, FileType, Path } = ctx.request.body
      if (!PumpRoomNumber || !Node || !FileType || !Path) return (ctx.body = { code: 400, msg: '参数缺失' })
      const sentence = `INSERT INTO SecondaryWaterProgressNodeFiles (PumpRoomNumber, Node, FileType, Path, UploadPerson) VALUES (@PumpRoomNumber, @Node, @FileType, @Path, @UploadPerson)`
      await queryWithParams(sentence, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
        Node: { type: sql.Int, value: Node },
        FileType: { type: sql.VarChar, value: FileType },
        Path: { type: sql.VarChar, value: Path },
        UploadPerson: { type: sql.VarChar, value: ctx.user.Name }
      })
      ctx.body = { code: 200, message: '添加成功' }
    } catch (error) {
      ctx.body = { code: 400, message: '添加失败', data: error.message }
    }
  }
  async update(ctx, next) {
    try {
      const { PumpRoomNumber, Node, FileType, Path } = ctx.request.body
      if (!PumpRoomNumber || !Node || !FileType || !Path) return (ctx.body = { code: 400, msg: '参数缺失' })
      const { recordsets } = await queryWithParams(`SELECT * FROM SecondaryWaterProgressNodeFiles WHERE PumpRoomNumber = @PumpRoomNumber AND Node = @Node AND FileType = @FileType`, {
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
        Node: { type: sql.Int, value: Node },
        FileType: { type: sql.VarChar, value: FileType }
      })
      if (!recordsets[0].length) return (ctx.body = { code: 400, msg: '未找到该条记录' })
      const sentence = `UPDATE SecondaryWaterProgressNodeFiles SET Path = @Path, UploadPerson = @UploadPerson WHERE PumpRoomNumber = @PumpRoomNumber AND Node = @Node AND FileType = @FileType`
      await queryWithParams(sentence, {
        Path: { type: sql.VarChar, value: Path },
        UploadPerson: { type: sql.VarChar, value: ctx.user.Name },
        PumpRoomNumber: { type: sql.VarChar, value: PumpRoomNumber },
        Node: { type: sql.Int, value: Node },
        FileType: { type: sql.VarChar, value: FileType }
      })
    } catch (error) {
      ctx.body = { code: 400, message: '修改失败', data: error.message }
    }
  }
}

// 二供巡检任务
class PumpHouseTaskController {
  // 创建巡检任务
  async create(ctx) {
    const { TaskScope = '', TaskContent, TaskEndTime, TaskRemark } = ctx.request.body
    const sentence = `INSERT INTO dbo.[SecondaryWaterTask] (TaskScope,TaskContent,TaskEndTime,TaskRemark,Initiator) VALUES (@TaskScope,@TaskContent,@TaskEndTime,@TaskRemark,@Initiator);`
    await queryWithParams(sentence, {
      TaskScope: { type: sql.VarChar, value: TaskScope },
      TaskContent: { type: sql.VarChar, value: TaskContent },
      TaskEndTime: { type: sql.DateTime, value: TaskEndTime },
      TaskRemark: { type: sql.VarChar, value: TaskRemark },
      Initiator: { type: sql.Int, value: ctx.user.id }
    })
    ctx.body = { code: 200, msg: '创建成功' }
  }

  // 发布者查询任务列表
  async list(ctx) {
    try {
      const { page = 1, pageSize = 10, startTime, endTime, initiator } = ctx.query

      // 分页参数处理
      const pageNum = page ? (Number(page) > 0 ? Number(page) : 1) : null
      const sizeNum = pageSize ? (Number(pageSize) > 0 ? Number(pageSize) : 10) : null

      // 构建WHERE条件
      let whereClause = ''
      const params = {}
      const conditions = []

      // 创建时间范围筛选（修改为CreateTime）
      if (startTime && endTime) {
        conditions.push('t.CreateTime >= @startTime AND t.CreateTime <= @endTime')
        params.startTime = { type: sql.DateTime, value: startTime }
        params.endTime = { type: sql.DateTime, value: endTime }
      }

      // 发起人筛选
      if (initiator) {
        conditions.push('t.Initiator = @initiator')
        params.initiator = { type: sql.Int, value: Number(initiator) }
      }

      if (conditions.length > 0) {
        whereClause = `WHERE ${conditions.join(' AND ')}`
      }

      // 优化后的查询SQL，包含整体巡检进度
      const optimizedQuery = `
        WITH TaskProgress AS (
          -- 计算每个任务的整体巡检进度
          SELECT
            t.TaskID,
            COUNT(DISTINCT p.PumpRoomNumber) AS totalPumpRooms,
            COUNT(DISTINCT r.PumpRoomNumber) AS completedPumpRooms
          FROM dbo.[SecondaryWaterTask] t
          CROSS JOIN dbo.[SecondaryWaterPatrol] p
          LEFT JOIN dbo.[SecondaryWaterRecord] r ON r.TaskID = t.TaskID AND r.PumpRoomNumber = p.PumpRoomNumber
          ${whereClause}
          GROUP BY t.TaskID
        )
        SELECT
          t.*,
          ISNULL(tp.totalPumpRooms, 0) AS totalPumpRooms,
          ISNULL(tp.completedPumpRooms, 0) AS completedPumpRooms,
          CASE
            WHEN ISNULL(tp.totalPumpRooms, 0) > 0
            THEN ROUND((CAST(ISNULL(tp.completedPumpRooms, 0) AS FLOAT) / CAST(tp.totalPumpRooms AS FLOAT)) * 100, 0)
            ELSE 0
          END AS progressPercentage
        FROM dbo.[SecondaryWaterTask] t
        LEFT JOIN TaskProgress tp ON t.TaskID = tp.TaskID
        ${whereClause}
        ORDER BY t.CreateTime DESC
      `

      // 如果有分页参数，执行分页查询
      if (pageNum && sizeNum) {
        // 查询总数
        const countSql = `SELECT COUNT(*) as total FROM dbo.[SecondaryWaterTask] t ${whereClause}`
        const { recordsets: countSets } = await queryWithParams(countSql, params)
        const total = countSets[0][0].total

        // 查询分页数据
        const offset = (pageNum - 1) * sizeNum
        const paginatedQuery = `${optimizedQuery} OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`

        params.offset = { type: sql.Int, value: offset }
        params.sizeNum = { type: sql.Int, value: sizeNum }

        const { recordsets } = await queryWithParams(paginatedQuery, params)

        // 转换数据格式
        const tasksWithProgress = recordsets[0].map((task) => {
          const { totalPumpRooms, completedPumpRooms, progressPercentage, ...taskData } = task
          return {
            ...taskData,
            inspectionProgress: {
              totalPumpRooms: totalPumpRooms || 0,
              completedPumpRooms: completedPumpRooms || 0,
              progressPercentage: progressPercentage || 0
            }
          }
        })

        // 计算分页信息
        const totalPages = Math.ceil(total / sizeNum)

        ctx.body = {
          code: 200,
          message: '查询成功',
          data: tasksWithProgress,
          pagination: {
            page: pageNum,
            pageSize: sizeNum,
            total,
            totalPages,
            hasNext: pageNum < totalPages,
            hasPrev: pageNum > 1
          }
        }
      } else {
        // 不分页，返回所有数据
        const { recordsets } = await queryWithParams(optimizedQuery, params)

        // 转换数据格式
        const tasksWithProgress = recordsets[0].map((task) => {
          const { totalPumpRooms, completedPumpRooms, progressPercentage, ...taskData } = task
          return {
            ...taskData,
            inspectionProgress: {
              totalPumpRooms: totalPumpRooms || 0,
              completedPumpRooms: completedPumpRooms || 0,
              progressPercentage: progressPercentage || 0
            }
          }
        })

        ctx.body = { code: 200, message: '查询成功', data: tasksWithProgress }
      }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  // 查询待巡查的列表
  async pendingList(ctx) {
    try {
      const { end = '0', page = 1, pageSize = 10 } = ctx.query
      const userId = ctx.user.id

      // 分页参数处理
      const pageNum = page ? (Number(page) > 0 ? Number(page) : 1) : null
      const sizeNum = pageSize ? (Number(pageSize) > 0 ? Number(pageSize) : 10) : null

      // 构建时间条件
      const timeCondition = end == '0' ? 'TaskEndTime >= GETDATE()' : 'TaskEndTime <= GETDATE()'

      // 一次性查询所有任务及其进度信息的复杂SQL
      const optimizedQuery = `
        WITH UserPumpRooms AS (
          -- 获取用户负责的所有泵房
          SELECT PumpRoomNumber
          FROM dbo.[SecondaryWaterPatrol]
          WHERE PrimaryResponsiblePerson = @userId OR SecondaryResponsiblePerson = @userId
        ),
        TaskProgress AS (
          -- 计算每个任务的完成进度
          SELECT
            t.TaskID,
            COUNT(DISTINCT upr.PumpRoomNumber) AS totalPumpRooms,
            COUNT(DISTINCT CASE
              WHEN r.PumpRoomNumber IS NOT NULL THEN r.PumpRoomNumber
              ELSE NULL
            END) AS completedPumpRooms
          FROM dbo.[SecondaryWaterTask] t
          CROSS JOIN UserPumpRooms upr
          LEFT JOIN dbo.[SecondaryWaterRecord] r ON r.TaskID = t.TaskID AND r.PumpRoomNumber = upr.PumpRoomNumber
          WHERE ${timeCondition}
          GROUP BY t.TaskID
        )
        SELECT
          t.*,
          ISNULL(tp.totalPumpRooms, 0) AS totalPumpRooms,
          ISNULL(tp.completedPumpRooms, 0) AS completedPumpRooms,
          CASE
            WHEN ISNULL(tp.totalPumpRooms, 0) > 0
            THEN ROUND((CAST(ISNULL(tp.completedPumpRooms, 0) AS FLOAT) / CAST(tp.totalPumpRooms AS FLOAT)) * 100, 0)
            ELSE 0
          END AS progressPercentage
        FROM dbo.[SecondaryWaterTask] t
        LEFT JOIN TaskProgress tp ON t.TaskID = tp.TaskID
        WHERE ${timeCondition}
        ORDER BY t.TaskEndTime ASC
      `

      // 如果有分页参数，执行分页查询
      // 查询总数
      const countQuery = `
          WITH UserPumpRooms AS (
            SELECT PumpRoomNumber
            FROM dbo.[SecondaryWaterPatrol]
            WHERE PrimaryResponsiblePerson = @userId OR SecondaryResponsiblePerson = @userId
          )
          SELECT COUNT(DISTINCT t.TaskID) as total
          FROM dbo.[SecondaryWaterTask] t
          CROSS JOIN UserPumpRooms upr
          WHERE ${timeCondition}
        `

      // 分页查询
      const offset = (pageNum - 1) * sizeNum
      const paginatedQuery = `${optimizedQuery} OFFSET @offset ROWS FETCH NEXT @sizeNum ROWS ONLY`

      // 并行执行三个查询
      const [tasksResult, countResult] = await Promise.all([
        queryWithParams(paginatedQuery, {
          userId: { type: sql.Int, value: userId },
          offset: { type: sql.Int, value: offset },
          sizeNum: { type: sql.Int, value: sizeNum }
        }),
        queryWithParams(countQuery, { userId: { type: sql.Int, value: userId } })
      ])

      const tasks = tasksResult.recordsets[0]
      const total = countResult.recordsets[0][0].total

      // 转换数据格式
      const tasksWithProgress = tasks.map((task) => {
        const { totalPumpRooms, completedPumpRooms, progressPercentage, ...taskData } = task
        return {
          ...taskData,
          inspectionProgress: {
            totalPumpRooms: totalPumpRooms || 0,
            completedPumpRooms: completedPumpRooms || 0,
            progressPercentage: progressPercentage || 0
          }
        }
      })

      // 计算分页信息
      const totalPages = Math.ceil(total / sizeNum)

      ctx.body = {
        code: 200,
        message: '查询成功',
        data: tasksWithProgress,
        pagination: {
          page: pageNum,
          pageSize: sizeNum,
          total,
          totalPages,
          hasNext: pageNum < totalPages,
          hasPrev: pageNum > 1
        }
      }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  // 查询具体的巡查点
  async inspectionPoint(ctx) {
    try {
      const { TaskID } = ctx.query
      const userId = ctx.user.id

      if (!TaskID) {
        return (ctx.body = { code: 400, message: '参数错误', data: 'TaskID不能为空' })
      }

      // 优化的SQL查询，一次性获取所有需要的信息
      const optimizedQuery = `
        WITH UserPumpRooms AS (
          -- 获取用户负责的泵房编号
          SELECT
            patrol.PumpRoomNumber,
            patrol.OptimalInspectionRoute,
            patrol.PrimaryResponsiblePerson,
            patrol.SecondaryResponsiblePerson
          FROM dbo.[SecondaryWaterPatrol] patrol
          WHERE patrol.PrimaryResponsiblePerson = @userId OR patrol.SecondaryResponsiblePerson = @userId
        )
        SELECT
          upr.PumpRoomNumber,
          upr.OptimalInspectionRoute,
          -- upr.PrimaryResponsiblePerson,
          -- upr.SecondaryResponsiblePerson,
          -- 从SecondaryWaterProgressNew表获取泵房基本信息（简化）
          swp.PumpHouseName,
          swp.CurrentNode,
          swp.X,
          swp.Y,
          -- 检查是否已巡检过
          CASE
            WHEN swr.PumpRoomNumber IS NOT NULL THEN 1
            ELSE 0
          END AS IsInspected,
          -- 巡检记录信息（使用通用字段）
          swr.TaskID,
          swr.CreateTime AS InspectionTime,
          swr.CreateBy AS InspectionBy
        FROM UserPumpRooms upr
        -- 关联泵房基本信息
        LEFT JOIN dbo.[SecondaryWaterProgressNew] swp ON upr.PumpRoomNumber = swp.PumpRoomNumber
        -- 关联巡检记录
        LEFT JOIN dbo.[SecondaryWaterRecord] swr ON swr.PumpRoomNumber = upr.PumpRoomNumber AND swr.TaskID = @taskId
        ORDER BY upr.OptimalInspectionRoute ASC, upr.PumpRoomNumber ASC
      `

      // 查询任务信息
      const taskQuery = `
        SELECT *,
        CASE
          WHEN TaskEndTime < GETDATE() THEN 1
          ELSE 0
        END AS IsEnd
        FROM dbo.[SecondaryWaterTask]
        WHERE TaskID = @taskId
      `

      // 并行执行两个查询
      const [pumpRoomsResult, taskResult] = await Promise.all([
        queryWithParams(optimizedQuery, {
          userId: { type: sql.Int, value: userId },
          taskId: { type: sql.Int, value: Number(TaskID) }
        }),
        queryWithParams(taskQuery, {
          taskId: { type: sql.Int, value: Number(TaskID) }
        })
      ])

      const pumpRooms = pumpRoomsResult.recordsets[0]
      const taskInfo = taskResult.recordsets[0][0]

      // 统计信息
      const totalCount = pumpRooms.length
      const inspectedCount = pumpRooms.filter((room) => room.IsInspected === 1).length
      const uninspectedCount = totalCount - inspectedCount
      const progressPercentage = totalCount > 0 ? Math.round((inspectedCount / totalCount) * 100) : 0

      ctx.body = {
        code: 200,
        message: '查询成功',
        data: {
          pumpRooms,
          taskInfo,
          statistics: {
            totalCount,
            inspectedCount,
            uninspectedCount,
            progressPercentage
          }
        }
      }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }

  // 统计巡查的完成情况
  async performance(ctx) {
    try {
      const { TaskID } = ctx.query

      if (!TaskID) {
        return (ctx.body = { code: 400, message: '参数错误', data: 'TaskID不能为空' })
      }

      // 复杂SQL查询，一次性获取所有统计信息
      const performanceQuery = `
        WITH TaskOverallStats AS (
          -- 任务整体完成情况
          SELECT
            COUNT(DISTINCT p.PumpRoomNumber) AS totalPumpRooms,
            COUNT(DISTINCT r.PumpRoomNumber) AS completedPumpRooms
          FROM dbo.[SecondaryWaterPatrol] p
          LEFT JOIN dbo.[SecondaryWaterRecord] r ON r.PumpRoomNumber = p.PumpRoomNumber AND r.TaskID = @taskId
        ),
        GridStats AS (
          -- 按网格统计
          SELECT
            ISNULL(p.Grid, '未分配网格') AS GridName,
            COUNT(DISTINCT p.PumpRoomNumber) AS totalPumpRooms,
            COUNT(DISTINCT r.PumpRoomNumber) AS completedPumpRooms,
            CASE
              WHEN COUNT(DISTINCT p.PumpRoomNumber) > 0
              THEN ROUND((CAST(COUNT(DISTINCT r.PumpRoomNumber) AS FLOAT) / CAST(COUNT(DISTINCT p.PumpRoomNumber) AS FLOAT)) * 100, 0)
              ELSE 0
            END AS progressPercentage
          FROM dbo.[SecondaryWaterPatrol] p
          LEFT JOIN dbo.[SecondaryWaterRecord] r ON r.PumpRoomNumber = p.PumpRoomNumber AND r.TaskID = @taskId
          GROUP BY p.Grid
        ),
        ResponsiblePersonStats AS (
          -- 按主要责任人统计
          SELECT
            p.Grid,
            p.PrimaryResponsiblePerson AS ResponsiblePersonId,
            u.Name AS ResponsiblePersonName,
            u.Username AS ResponsiblePersonUsername,
            u.Station AS ResponsiblePersonStation,
            'primary' AS ResponsibleType,
            COUNT(DISTINCT p.PumpRoomNumber) AS totalPumpRooms,
            COUNT(DISTINCT r.PumpRoomNumber) AS completedPumpRooms,
            CASE
              WHEN COUNT(DISTINCT p.PumpRoomNumber) > 0
              THEN ROUND((CAST(COUNT(DISTINCT r.PumpRoomNumber) AS FLOAT) / CAST(COUNT(DISTINCT p.PumpRoomNumber) AS FLOAT)) * 100, 0)
              ELSE 0
            END AS progressPercentage
          FROM dbo.[SecondaryWaterPatrol] p
          LEFT JOIN dbo.[SecondaryWaterRecord] r ON r.PumpRoomNumber = p.PumpRoomNumber AND r.TaskID = @taskId
          LEFT JOIN dbo.[User] u ON u.id = p.PrimaryResponsiblePerson
          WHERE p.PrimaryResponsiblePerson IS NOT NULL
          GROUP BY p.Grid, p.PrimaryResponsiblePerson, u.Name, u.Username, u.Station

          UNION ALL

          -- 按次要责任人统计
          SELECT
            p.Grid,
            p.SecondaryResponsiblePerson AS ResponsiblePersonId,
            u.Name AS ResponsiblePersonName,
            u.Username AS ResponsiblePersonUsername,
            u.Station AS ResponsiblePersonStation,
            'secondary' AS ResponsibleType,
            COUNT(DISTINCT p.PumpRoomNumber) AS totalPumpRooms,
            COUNT(DISTINCT r.PumpRoomNumber) AS completedPumpRooms,
            CASE
              WHEN COUNT(DISTINCT p.PumpRoomNumber) > 0
              THEN ROUND((CAST(COUNT(DISTINCT r.PumpRoomNumber) AS FLOAT) / CAST(COUNT(DISTINCT p.PumpRoomNumber) AS FLOAT)) * 100, 0)
              ELSE 0
            END AS progressPercentage
          FROM dbo.[SecondaryWaterPatrol] p
          LEFT JOIN dbo.[SecondaryWaterRecord] r ON r.PumpRoomNumber = p.PumpRoomNumber AND r.TaskID = @taskId
          LEFT JOIN dbo.[User] u ON u.id = p.SecondaryResponsiblePerson
          WHERE p.SecondaryResponsiblePerson IS NOT NULL
          GROUP BY p.Grid, p.SecondaryResponsiblePerson, u.Name, u.Username, u.Station
        )
        SELECT
          'overall' AS StatType,
          NULL AS GridName,
          NULL AS ResponsiblePersonId,
          NULL AS ResponsiblePersonName,
          NULL AS ResponsiblePersonUsername,
          NULL AS ResponsiblePersonStation,
          NULL AS ResponsibleType,
          totalPumpRooms,
          completedPumpRooms,
          CASE
            WHEN totalPumpRooms > 0
            THEN ROUND((CAST(completedPumpRooms AS FLOAT) / CAST(totalPumpRooms AS FLOAT)) * 100, 0)
            ELSE 0
          END AS progressPercentage
        FROM TaskOverallStats

        UNION ALL

        SELECT
          'grid' AS StatType,
          GridName,
          NULL AS ResponsiblePersonId,
          NULL AS ResponsiblePersonName,
          NULL AS ResponsiblePersonUsername,
          NULL AS ResponsiblePersonStation,
          NULL AS ResponsibleType,
          totalPumpRooms,
          completedPumpRooms,
          progressPercentage
        FROM GridStats

        UNION ALL

        SELECT
          'person' AS StatType,
          Grid AS GridName,
          ResponsiblePersonId,
          ResponsiblePersonName,
          ResponsiblePersonUsername,
          ResponsiblePersonStation,
          ResponsibleType,
          totalPumpRooms,
          completedPumpRooms,
          progressPercentage
        FROM ResponsiblePersonStats

        ORDER BY StatType, GridName, ResponsibleType, ResponsiblePersonName
      `

      // 查询任务信息
      const taskQuery = `
        SELECT *,
        CASE
          WHEN TaskEndTime < GETDATE() THEN 1
          ELSE 0
        END AS IsEnd
        FROM dbo.[SecondaryWaterTask]
        WHERE TaskID = @taskId
      `

      // 并行执行查询
      const [performanceResult, taskResult] = await Promise.all([queryWithParams(performanceQuery, { taskId: { type: sql.Int, value: Number(TaskID) } }), queryWithParams(taskQuery, { taskId: { type: sql.Int, value: Number(TaskID) } })])

      const rawStats = performanceResult.recordsets[0]
      const taskInfo = taskResult.recordsets[0][0]

      // 分类处理统计数据
      const overallStats = rawStats.find((stat) => stat.StatType === 'overall')
      const gridStats = rawStats.filter((stat) => stat.StatType === 'grid')
      const personStats = rawStats.filter((stat) => stat.StatType === 'person')

      // 按网格组织责任人数据，并按完成度排序
      const gridWithPersons = gridStats
        // 先按网格完成度排序（降序）
        .sort((a, b) => b.progressPercentage - a.progressPercentage)
        .map((grid) => {
          const personsInGrid = personStats.filter((person) => person.GridName === grid.GridName)

          // 分别处理主要责任人和次要责任人，并按完成度排序
          const primaryPersons = personsInGrid
            .filter((person) => person.ResponsibleType === 'primary')
            .sort((a, b) => b.progressPercentage - a.progressPercentage) // 按完成度降序排序
            .map((person) => ({
              userId: person.ResponsiblePersonId,
              name: person.ResponsiblePersonName,
              username: person.ResponsiblePersonUsername,
              station: person.ResponsiblePersonStation,
              type: 'primary',
              totalPumpRooms: person.totalPumpRooms,
              completedPumpRooms: person.completedPumpRooms,
              progressPercentage: person.progressPercentage
            }))

          const secondaryPersons = personsInGrid
            .filter((person) => person.ResponsibleType === 'secondary')
            .sort((a, b) => b.progressPercentage - a.progressPercentage) // 按完成度降序排序
            .map((person) => ({
              userId: person.ResponsiblePersonId,
              name: person.ResponsiblePersonName,
              username: person.ResponsiblePersonUsername,
              station: person.ResponsiblePersonStation,
              type: 'secondary',
              totalPumpRooms: person.totalPumpRooms,
              completedPumpRooms: person.completedPumpRooms,
              progressPercentage: person.progressPercentage
            }))

          return {
            gridName: grid.GridName,
            totalPumpRooms: grid.totalPumpRooms,
            completedPumpRooms: grid.completedPumpRooms,
            progressPercentage: grid.progressPercentage,
            responsiblePersons: {
              primary: primaryPersons,
              secondary: secondaryPersons
            }
          }
        })

      ctx.body = {
        code: 200,
        message: '查询成功',
        data: {
          taskInfo,
          overallProgress: {
            totalPumpRooms: overallStats?.totalPumpRooms || 0,
            completedPumpRooms: overallStats?.completedPumpRooms || 0,
            progressPercentage: overallStats?.progressPercentage || 0
          },
          gridProgress: gridWithPersons
        }
      }
    } catch (error) {
      ctx.body = { code: 400, message: '查询失败', data: error.message }
    }
  }
}

const pumpHouse = new PumpHouseController()
const node = new PumpHouseNodeController()
const nodeFile = new PumpHouseNodeFileController()
const task = new PumpHouseTaskController()

module.exports = { pumpHouse, node, nodeFile, task }
