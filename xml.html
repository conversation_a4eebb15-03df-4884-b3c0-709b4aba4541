<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>设备数据处理系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
      body {
        font-family: 'Microsoft YaHei', Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        padding: 30px;
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
        color: #333;
      }

      .upload-section {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 40px;
        text-align: center;
        margin-bottom: 30px;
        transition: border-color 0.3s;
      }

      .upload-section:hover {
        border-color: #007bff;
      }

      .upload-section.dragover {
        border-color: #007bff;
        background-color: #f8f9fa;
      }

      .file-input {
        display: none;
      }

      .upload-btn {
        background: #007bff;
        color: white;
        padding: 12px 24px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 16px;
        transition: background-color 0.3s;
      }

      .upload-btn:hover {
        background: #0056b3;
      }

      .file-info {
        margin-top: 20px;
        padding: 15px;
        background: #e9ecef;
        border-radius: 6px;
        display: none;
      }

      .controls {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        flex-wrap: wrap;
        gap: 10px;
      }

      .btn {
        padding: 10px 20px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.3s;
      }

      .btn-primary {
        background: #007bff;
        color: white;
      }

      .btn-primary:hover {
        background: #0056b3;
      }

      .btn-success {
        background: #28a745;
        color: white;
      }

      .btn-success:hover {
        background: #1e7e34;
      }

      .btn:disabled {
        background: #6c757d;
        cursor: not-allowed;
      }

      .data-info {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
        flex-wrap: wrap;
      }

      .info-card {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 6px;
        border-left: 4px solid #007bff;
        flex: 1;
        min-width: 200px;
      }

      .info-card h4 {
        margin: 0 0 10px 0;
        color: #333;
      }

      .info-card p {
        margin: 0;
        font-size: 18px;
        font-weight: bold;
        color: #007bff;
      }

      .table-container {
        max-height: 500px;
        overflow: auto;
        border: 1px solid #ddd;
        border-radius: 6px;
        margin-bottom: 20px;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        background: white;
      }

      th,
      td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #ddd;
      }

      th {
        background: #f8f9fa;
        font-weight: bold;
        position: sticky;
        top: 0;
        z-index: 10;
      }

      tr:hover {
        background: #f8f9fa;
      }

      .loading {
        text-align: center;
        padding: 40px;
        color: #666;
      }

      .error {
        color: #dc3545;
        background: #f8d7da;
        padding: 15px;
        border-radius: 6px;
        margin: 20px 0;
      }

      .success {
        color: #155724;
        background: #d4edda;
        padding: 15px;
        border-radius: 6px;
        margin: 20px 0;
      }

      .progress-bar {
        width: 100%;
        height: 20px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin: 20px 0;
        display: none;
      }

      .progress-fill {
        height: 100%;
        background: #007bff;
        width: 0%;
        transition: width 0.3s;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>设备数据处理系统</h1>
        <p>支持Excel文件上传，自动处理6秒颗粒度数据转换为1分钟颗粒度</p>
      </div>

      <div class="upload-section" id="uploadSection">
        <div>
          <h3>选择或拖拽Excel文件</h3>
          <p>支持 .xlsx, .xls 格式文件</p>
          <button class="upload-btn" onclick="document.getElementById('fileInput').click()">选择文件</button>
          <input type="file" id="fileInput" class="file-input" accept=".xlsx,.xls" />
        </div>
        <div class="file-info" id="fileInfo">
          <strong>已选择文件：</strong><span id="fileName"></span><br />
          <strong>文件大小：</strong><span id="fileSize"></span>
        </div>
      </div>

      <div class="progress-bar" id="progressBar">
        <div class="progress-fill" id="progressFill"></div>
      </div>

      <div class="controls">
        <div>
          <button class="btn btn-primary" id="processBtn" onclick="processData()" disabled>处理数据</button>
          <button class="btn btn-success" id="exportBtn" onclick="exportData()" disabled>导出处理后数据</button>
        </div>
        <div>
          <label>
            <input type="checkbox" id="showOriginal" onchange="toggleDataView()" />
            显示原始数据
          </label>
        </div>
      </div>

      <div class="data-info" id="dataInfo" style="display: none">
        <div class="info-card">
          <h4>原始数据</h4>
          <p id="originalCount">0 条</p>
        </div>
        <div class="info-card">
          <h4>处理后数据</h4>
          <p id="processedCount">0 条</p>
        </div>
        <div class="info-card">
          <h4>数据时间范围</h4>
          <p id="timeRange">-</p>
        </div>
      </div>

      <div class="table-container" id="tableContainer" style="display: none">
        <table id="dataTable">
          <thead id="tableHead"></thead>
          <tbody id="tableBody"></tbody>
        </table>
      </div>

      <div id="messageArea"></div>
    </div>

    <script>
      let originalData = []
      let processedData = []
      let currentFile = null

      // 文件上传处理
      document.getElementById('fileInput').addEventListener('change', handleFileSelect)

      // 拖拽上传
      const uploadSection = document.getElementById('uploadSection')
      uploadSection.addEventListener('dragover', handleDragOver)
      uploadSection.addEventListener('drop', handleDrop)
      uploadSection.addEventListener('dragleave', handleDragLeave)

      function handleFileSelect(event) {
        const file = event.target.files[0]
        if (file) {
          currentFile = file
          displayFileInfo(file)
          readExcelFile(file)
        }
      }

      function handleDragOver(event) {
        event.preventDefault()
        uploadSection.classList.add('dragover')
      }

      function handleDrop(event) {
        event.preventDefault()
        uploadSection.classList.remove('dragover')

        const files = event.dataTransfer.files
        if (files.length > 0) {
          const file = files[0]
          if (file.type.includes('sheet') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
            currentFile = file
            displayFileInfo(file)
            readExcelFile(file)
          } else {
            showMessage('请选择Excel文件（.xlsx 或 .xls）', 'error')
          }
        }
      }

      function handleDragLeave(event) {
        uploadSection.classList.remove('dragover')
      }

      function displayFileInfo(file) {
        document.getElementById('fileName').textContent = file.name
        document.getElementById('fileSize').textContent = formatFileSize(file.size)
        document.getElementById('fileInfo').style.display = 'block'
      }

      function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes'
        const k = 1024
        const sizes = ['Bytes', 'KB', 'MB', 'GB']
        const i = Math.floor(Math.log(bytes) / Math.log(k))
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
      }

      function readExcelFile(file) {
        showProgress(true)
        updateProgress(20)

        const reader = new FileReader()
        reader.onload = function (e) {
          try {
            updateProgress(50)
            const data = new Uint8Array(e.target.result)
            const workbook = XLSX.read(data, { type: 'array' })

            // 读取第一个工作表
            const firstSheetName = workbook.SheetNames[0]
            const worksheet = workbook.Sheets[firstSheetName]

            // 转换为JSON
            const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

            updateProgress(80)
            parseExcelData(jsonData)
            updateProgress(100)

            setTimeout(() => showProgress(false), 500)
          } catch (error) {
            showProgress(false)
            showMessage('文件读取失败：' + error.message, 'error')
          }
        }

        reader.onerror = function () {
          showProgress(false)
          showMessage('文件读取失败', 'error')
        }

        reader.readAsArrayBuffer(file)
      }

      function parseExcelData(jsonData) {
        if (jsonData.length < 2) {
          showMessage('Excel文件数据不足', 'error')
          return
        }

        // 获取表头
        const headers = jsonData[0]

        // 查找采集时间和末端压力列的索引
        let timeIndex = -1
        let pressureIndex = -1

        headers.forEach((header, index) => {
          if (header && header.toString().includes('采集时间')) {
            timeIndex = index
          }
          if (header && header.toString().includes('末端压力')) {
            pressureIndex = index
          }
        })

        if (timeIndex === -1 || pressureIndex === -1) {
          showMessage('未找到"采集时间"或"末端压力"列，请检查Excel文件格式', 'error')
          return
        }

        // 解析数据
        originalData = []
        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i]
          if (row[timeIndex] && row[pressureIndex] !== undefined) {
            originalData.push({
              time: row[timeIndex],
              pressure: parseFloat(row[pressureIndex]) || 0,
              rowIndex: i
            })
          }
        }

        if (originalData.length === 0) {
          showMessage('未找到有效数据', 'error')
          return
        }

        // 显示原始数据信息
        document.getElementById('originalCount').textContent = originalData.length + ' 条'
        document.getElementById('dataInfo').style.display = 'flex'
        document.getElementById('processBtn').disabled = false

        // 显示数据表格
        displayData(originalData, '原始数据')

        showMessage('Excel文件读取成功，共 ' + originalData.length + ' 条数据', 'success')
      }

      function processData() {
        if (originalData.length === 0) {
          showMessage('请先上传Excel文件', 'error')
          return
        }

        showProgress(true)
        updateProgress(10)

        try {
          // 数据处理逻辑
          processedData = processDeviceData(originalData)

          updateProgress(80)

          // 更新显示
          document.getElementById('processedCount').textContent = processedData.length + ' 条'
          document.getElementById('exportBtn').disabled = false

          // 显示处理后的数据
          if (!document.getElementById('showOriginal').checked) {
            displayData(processedData, '处理后数据')
          }

          updateProgress(100)
          setTimeout(() => showProgress(false), 500)

          showMessage('数据处理完成，共生成 ' + processedData.length + ' 条1分钟颗粒度数据', 'success')
        } catch (error) {
          showProgress(false)
          showMessage('数据处理失败：' + error.message, 'error')
        }
      }

      function processDeviceData(data) {
        // 按时间排序
        const sortedData = data.slice().sort((a, b) => {
          const timeA = parseTime(a.time)
          const timeB = parseTime(b.time)
          return timeA - timeB
        })

        if (sortedData.length === 0) return []

        const result = []
        const startTime = parseTime(sortedData[0].time)
        const endTime = parseTime(sortedData[sortedData.length - 1].time)

        // 计算时间范围
        const startDate = new Date(startTime)
        const endDate = new Date(endTime)
        document.getElementById('timeRange').textContent = formatDateTime(startDate) + ' ~ ' + formatDateTime(endDate)

        // 生成每分钟的时间点（一天1440分钟）
        const dayStart = new Date(startDate)
        dayStart.setHours(0, 0, 0, 0)

        let lastValidPressure = 0
        let dataIndex = 0

        for (let minute = 0; minute < 1440; minute++) {
          const currentMinute = new Date(dayStart.getTime() + minute * 60 * 1000)
          const minuteStart = currentMinute.getTime()
          const minuteEnd = minuteStart + 60 * 1000

          // 查找这一分钟内的第一条数据
          let foundData = null

          // 从当前位置开始查找
          for (let i = dataIndex; i < sortedData.length; i++) {
            const dataTime = parseTime(sortedData[i].time)

            if (dataTime >= minuteStart && dataTime < minuteEnd) {
              foundData = sortedData[i]
              dataIndex = i // 更新查找起始位置
              break
            } else if (dataTime >= minuteEnd) {
              break // 超出当前分钟范围
            }
          }

          let pressure
          if (foundData) {
            pressure = foundData.pressure / 1000 // 除以1000
            lastValidPressure = pressure
          } else {
            // 使用上一次的有效数据
            pressure = lastValidPressure
          }

          result.push({
            time: formatDateTime(currentMinute),
            pressure: pressure.toFixed(3),
            source: foundData ? 'original' : 'interpolated'
          })
        }

        return result
      }

      function parseTime(timeStr) {
        // 处理各种时间格式
        if (typeof timeStr === 'number') {
          // Excel日期序列号
          return new Date((timeStr - 25569) * 86400 * 1000).getTime()
        }

        if (typeof timeStr === 'string') {
          // 尝试直接解析
          const date = new Date(timeStr)
          if (!isNaN(date.getTime())) {
            return date.getTime()
          }

          // 尝试其他格式
          const formats = [/(\d{4})[\/\-](\d{1,2})[\/\-](\d{1,2})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})/, /(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})\s+(\d{1,2}):(\d{1,2}):(\d{1,2})/]

          for (const format of formats) {
            const match = timeStr.match(format)
            if (match) {
              const [, p1, p2, p3, h, m, s] = match
              let year, month, day

              if (p1.length === 4) {
                ;[year, month, day] = [p1, p2, p3]
              } else {
                ;[month, day, year] = [p1, p2, p3]
              }

              return new Date(year, month - 1, day, h, m, s).getTime()
            }
          }
        }

        return new Date().getTime() // 默认返回当前时间
      }

      function formatDateTime(date) {
        const year = date.getFullYear()
        const month = String(date.getMonth() + 1).padStart(2, '0')
        const day = String(date.getDate()).padStart(2, '0')
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      }

      function displayData(data, title) {
        if (data.length === 0) return

        const tableContainer = document.getElementById('tableContainer')
        const tableHead = document.getElementById('tableHead')
        const tableBody = document.getElementById('tableBody')

        // 清空表格
        tableHead.innerHTML = ''
        tableBody.innerHTML = ''

        // 创建表头
        const headerRow = document.createElement('tr')
        const headers = ['序号', '采集时间', '末端压力(MPa)']
        if (data[0].source) {
          headers.push('数据来源')
        }

        headers.forEach((header) => {
          const th = document.createElement('th')
          th.textContent = header
          headerRow.appendChild(th)
        })
        tableHead.appendChild(headerRow)

        // 创建数据行（只显示前1000行以提高性能）
        const displayCount = Math.min(data.length, 1000)
        for (let i = 0; i < displayCount; i++) {
          const row = document.createElement('tr')

          const indexCell = document.createElement('td')
          indexCell.textContent = i + 1
          row.appendChild(indexCell)

          const timeCell = document.createElement('td')
          timeCell.textContent = data[i].time
          row.appendChild(timeCell)

          const pressureCell = document.createElement('td')
          pressureCell.textContent = data[i].pressure
          row.appendChild(pressureCell)

          if (data[i].source) {
            const sourceCell = document.createElement('td')
            sourceCell.textContent = data[i].source === 'original' ? '原始数据' : '补充数据'
            sourceCell.style.color = data[i].source === 'original' ? '#28a745' : '#ffc107'
            row.appendChild(sourceCell)
          }

          tableBody.appendChild(row)
        }

        if (data.length > 1000) {
          const row = document.createElement('tr')
          const cell = document.createElement('td')
          cell.colSpan = headers.length
          cell.textContent = `... 还有 ${data.length - 1000} 条数据未显示`
          cell.style.textAlign = 'center'
          cell.style.color = '#666'
          row.appendChild(cell)
          tableBody.appendChild(row)
        }

        tableContainer.style.display = 'block'
      }

      function toggleDataView() {
        const showOriginal = document.getElementById('showOriginal').checked
        if (showOriginal && originalData.length > 0) {
          displayData(originalData, '原始数据')
        } else if (!showOriginal && processedData.length > 0) {
          displayData(processedData, '处理后数据')
        }
      }

      function exportData() {
        if (processedData.length === 0) {
          showMessage('请先处理数据', 'error')
          return
        }

        try {
          // 创建工作簿
          const wb = XLSX.utils.book_new()

          // 准备数据
          const exportData = [['序号', '采集时间', '末端压力(MPa)', '数据来源']]

          processedData.forEach((item, index) => {
            exportData.push([index + 1, item.time, item.pressure, item.source === 'original' ? '原始数据' : '补充数据'])
          })

          // 创建工作表
          const ws = XLSX.utils.aoa_to_sheet(exportData)

          // 设置列宽
          ws['!cols'] = [
            { width: 8 }, // 序号
            { width: 20 }, // 采集时间
            { width: 15 }, // 末端压力
            { width: 12 } // 数据来源
          ]

          // 添加工作表到工作簿
          XLSX.utils.book_append_sheet(wb, ws, '处理后数据')

          // 生成文件名
          const now = new Date()
          const fileName = `设备数据_处理后_${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(
            2,
            '0'
          )}.xlsx`

          // 下载文件
          XLSX.writeFile(wb, fileName)

          showMessage('数据导出成功：' + fileName, 'success')
        } catch (error) {
          showMessage('导出失败：' + error.message, 'error')
        }
      }

      function showProgress(show) {
        document.getElementById('progressBar').style.display = show ? 'block' : 'none'
        if (!show) {
          updateProgress(0)
        }
      }

      function updateProgress(percent) {
        document.getElementById('progressFill').style.width = percent + '%'
      }

      function showMessage(message, type) {
        const messageArea = document.getElementById('messageArea')
        const messageDiv = document.createElement('div')
        messageDiv.className = type
        messageDiv.textContent = message

        messageArea.innerHTML = ''
        messageArea.appendChild(messageDiv)

        // 3秒后自动清除消息
        setTimeout(() => {
          if (messageArea.contains(messageDiv)) {
            messageArea.removeChild(messageDiv)
          }
        }, 3000)
      }
    </script>
  </body>
</html>
